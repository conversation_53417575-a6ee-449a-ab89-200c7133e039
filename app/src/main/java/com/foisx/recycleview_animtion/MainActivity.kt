// MainActivity.kt
package com.foisx.recycleview_animtion

import android.os.Bundle
import android.widget.Button
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.foisx.recycleview_animtion.adapter.ChatAdapter
import com.foisx.recycleview_animtion.manager.ChatLayoutManager
import com.foisx.recycleview_animtion.model.ChatMessage
import com.foisx.recycleview_animtion.model.MessageType

class MainActivity : AppCompatActivity() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var btnAddEntrance: Button
    private lateinit var btnAddSpeech: Button
    private lateinit var adapter: ChatAdapter
    private lateinit var chatLayoutManager: ChatLayoutManager

    // 模拟数据
    private val usernames = listOf(
        "小明", "小红", "小刚", "小丽", "小华", "小强", "小美", "小亮",
        "张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十"
    )

    private val speechContents = listOf(
        "大家好！", "今天天气真不错", "有人在吗？", "这个直播间很有趣",
        "主播加油！", "666", "哈哈哈", "太厉害了", "学到了很多",
        "感谢分享", "继续继续", "支持支持", "很棒的内容"
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContentView(R.layout.activity_main)
        setupWindowInsets()
        initViews()
        setupRecyclerView()
        setupClickListeners()
    }

    private fun initViews() {
        recyclerView = findViewById(R.id.recyclerView)
        btnAddEntrance = findViewById(R.id.btnAddEntrance)
        btnAddSpeech = findViewById(R.id.btnAddSpeech)
    }

    private fun setupWindowInsets() {
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main)) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
            insets
        }
    }

    private fun setupRecyclerView() {
        adapter = ChatAdapter()

        recyclerView.apply {
            layoutManager = LinearLayoutManager(this@MainActivity, LinearLayoutManager.VERTICAL, true)
            adapter = <EMAIL>
            itemAnimator = null // 禁用默认动画
        }

        // 使用完整的架构
        chatLayoutManager = ChatLayoutManager(recyclerView, adapter)
    }

    private fun setupClickListeners() {
        btnAddEntrance.setOnClickListener {
            addEntranceMessage()
        }

        btnAddSpeech.setOnClickListener {
            addSpeechMessage()
        }
    }

    private fun addEntranceMessage() {
        val randomUser = usernames.random()
        val entranceMessage = ChatMessage(
            type = MessageType.ENTRANCE,
            content = "$randomUser 进入了房间",
            username = randomUser
        )

        chatLayoutManager.addMessage(entranceMessage)
        updateStatusInfo()
    }

    private fun addSpeechMessage() {
        val randomUser = usernames.random()
        val randomContent = speechContents.random()

        val speechMessage = ChatMessage(
            type = MessageType.SPEECH,
            content = randomContent,
            username = randomUser
        )

        chatLayoutManager.addMessage(speechMessage)
        updateStatusInfo()
    }

    private fun updateStatusInfo() {
        val state = chatLayoutManager.getCurrentState()
        val queueSize = chatLayoutManager.getTransitionManager().getQueueSize()
        val messageCount = adapter.getMessageCount()
        val isTransitioning = chatLayoutManager.isInTransition()

        btnAddEntrance.text = "添加进场消息\n状态:$state 队列:$queueSize"
        btnAddSpeech.text = "添加发言消息\n总数:$messageCount"

        // 过渡期间禁用按钮
        btnAddEntrance.isEnabled = !isTransitioning
        btnAddSpeech.isEnabled = !isTransitioning

        if (isTransitioning) {
            recyclerView.postDelayed({
                btnAddEntrance.isEnabled = true
                btnAddSpeech.isEnabled = true
            }, 1000)
        }
    }
}


