package com.foisx.recycleview_animtion.manager

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.view.View
import androidx.core.util.Pools
import androidx.interpolator.view.animation.FastOutSlowInInterpolator
import androidx.recyclerview.widget.RecyclerView
import com.foisx.recycleview_animtion.model.ChatMessage

class OptimizedTransitionAnimator {
    private val animatorPool = Pools.SimplePool<AnimatorSet>(3)
    private val interpolator = FastOutSlowInInterpolator()

    data class TransitionParams(
        val exitDistance: Float,
        val enterDistance: Float,
        val animationDuration: Long = 350
    )

    fun createEntranceReplacementAnimation(
        recyclerView: RecyclerView,
        newMessage: ChatMessage,
        onComplete: () -> Unit
    ): Animator? {

        val firstViewHolder = recyclerView.findViewHolderForAdapterPosition(0)
        val firstView = firstViewHolder?.itemView ?: return null

        val params = calculateTransitionParams(firstView)

        return createOptimizedAnimation(firstView, params, onComplete)
    }

    private fun calculateTransitionParams(view: View): TransitionParams {
        return TransitionParams(
            // 使用view的高度作为动画距离
            exitDistance = view.height.toFloat(),
            enterDistance = view.height.toFloat(),
            animationDuration = 350
        )
    }

    private fun createOptimizedAnimation(
        view: View,
        params: TransitionParams,
        onComplete: () -> Unit
    ): Animator {

        // 尝试从池中获取AnimatorSet
        val animatorSet = animatorPool.acquire() ?: AnimatorSet()

        // 退出动画 - 向上滑出
        val exitAnimator = createExitAnimation(view, params)

        // 进入动画（在退出动画完成后执行）
        exitAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                // 更新数据
                onComplete()

                // 执行进入动画 - 从下往上进入
                val enterAnimator = createEnterAnimation(view, params)
                enterAnimator.addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        // 回收到池中
                        animatorPool.release(animatorSet)
                    }
                })
                enterAnimator.start()
            }
        })

        return exitAnimator
    }

    private fun createExitAnimation(view: View, params: TransitionParams): Animator {
        // 向上滑出并淡出
        val slideOut = ObjectAnimator.ofFloat(view, "translationY", 0f, -params.exitDistance)
        val fadeOut = ObjectAnimator.ofFloat(view, "alpha", 1f, 0f)

        return AnimatorSet().apply {
            playTogether(slideOut, fadeOut)
            duration = params.animationDuration
            interpolator = <EMAIL>
        }
    }

    private fun createEnterAnimation(view: View, params: TransitionParams): Animator {
        view.translationY = params.enterDistance
        view.alpha = 0f
        view.scaleX = 0.95f  // 轻微缩小
        view.scaleY = 0.95f

        val slideIn = ObjectAnimator.ofFloat(view, "translationY", params.enterDistance, 0f)
        val fadeIn = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)
        val scaleXIn = ObjectAnimator.ofFloat(view, "scaleX", 0.95f, 1f)
        val scaleYIn = ObjectAnimator.ofFloat(view, "scaleY", 0.95f, 1f)

        return AnimatorSet().apply {
            playTogether(slideIn, fadeIn, scaleXIn, scaleYIn)
            duration = params.animationDuration
            interpolator = <EMAIL>
        }
    }
}
